# MCP工具调用索引

> **最后更新**: 2025-01-29 20:30
> **工具总数**: 128个 (已验证65个，可用61个)
> **版本**: v2.4.0
> AI工具快速查找索引 - 按功能分类的完整工具列表

## 🔍 工具索引

### 🧠 思维分析
- `sequentialthinking_Sequential_thinking` - 复杂问题分步分析

### 🎯 PromptX专业能力增强
- `promptx_init_promptx` - 初始化PromptX工作环境，创建配置目录和项目资源注册表
- `promptx_welcome_promptx` - 展示所有可用的AI专业角色和工具清单
- `promptx_action_promptx` - 激活特定AI专业角色，获得专业思维和技能包
- `promptx_learn_promptx` - 学习专业资源，通过标准化协议体系加载各类专业资源
- `promptx_think_promptx` - 基于认知心理学的思维链式推理机制
- `promptx_remember_promptx` - 基于认知心理学模拟人类记忆编码-存储-检索机制
- `promptx_recall_promptx` - 基于检索心理学的记忆激活和提取机制
- `promptx_tool_promptx` - 执行通过@tool协议声明的JavaScript功能工具

### 🌐 浏览器操作 (Playwright)
- `browser_navigate_Playwright` - 导航到指定URL
- `browser_click_Playwright` - 在网页上执行点击操作
- `browser_type_Playwright` - 向可编辑元素输入文本
- `browser_take_screenshot_Playwright` - 截取当前页面的屏幕截图
- `browser_wait_for_Playwright` - 等待文本出现或消失或指定时间过去
- `browser_snapshot_Playwright` - 捕获当前页面的可访问性快照，比截图更好
- `browser_close_Playwright` - 关闭页面
- `browser_resize_Playwright` - 调整浏览器窗口大小
- `browser_hover_Playwright` - 悬停在页面元素上
- `browser_select_option_Playwright` - 在下拉菜单中选择选项
- `browser_handle_dialog_Playwright` - 处理对话框
- `browser_file_upload_Playwright` - 上传一个或多个文件
- `browser_press_key_Playwright` - 在键盘上按键
- `browser_navigate_back_Playwright` - 返回上一页
- `browser_navigate_forward_Playwright` - 前进到下一页
- `browser_tab_new_Playwright` - 打开新标签页
- `browser_tab_close_Playwright` - 关闭标签页
- `browser_tab_select_Playwright` - 通过索引选择标签页
- `browser_tab_list_Playwright` - 列出浏览器标签页
- `browser_drag_Playwright` - 在两个元素之间执行拖放
- `browser_console_messages_Playwright` - 返回所有控制台消息
- `browser_network_requests_Playwright` - 返回加载页面以来的所有网络请求
- `browser_evaluate_Playwright` - 在页面或元素上评估JavaScript表达式
- `browser_install_Playwright` - 安装配置中指定的浏览器

### 🔍 网络搜索
- `web_search_exa_Exa_Search` - 使用Exa AI进行实时网络搜索，可配置结果数量
- `brave_web_search_Brave_Search` - 使用Brave搜索API进行通用查询，支持分页和过滤
- `brave_local_search_Brave_Search` - 搜索本地商家和地点，返回详细商业信息
- `web-search` - 搜索网络信息，使用Google自定义搜索API
- `web-fetch` - 从网页获取数据并转换为Markdown
- `tavily_search_tavily-remote-mcp` - 实时网络信息搜索，支持多种搜索主题和过滤
- `tavily_extract_tavily-remote-mcp` - 从特定网页提取和处理内容，返回结构化格式
- `tavily_crawl_tavily-remote-mcp` - 从网站爬取多个页面，支持外部链接和分类过滤
- `tavily_map_tavily-remote-mcp` - 发现网站结构，映射所有URL和页面关系

### 🕷️ 网页抓取 (Firecrawl)
- `firecrawl_scrape_firecrawl-mcp` - 从单个URL提取内容，支持高级选项和缓存
- `firecrawl_map_firecrawl-mcp` - 发现网站所有索引URL，了解网站结构
- `firecrawl_crawl_firecrawl-mcp` - 异步爬取网站多个页面并提取内容
- `firecrawl_check_crawl_status_firecrawl-mcp` - 检查爬取任务状态和进度
- `firecrawl_search_firecrawl-mcp` - 网络搜索并可选择性提取搜索结果内容
- `firecrawl_extract_firecrawl-mcp` - 使用LLM从网页提取结构化信息
- `firecrawl_deep_research_firecrawl-mcp` - 进行深度网络研究，智能爬取和LLM分析
- `firecrawl_generate_llmstxt_firecrawl-mcp` - 为域名生成标准化llms.txt文件

### 🐙 GitHub集成
- `search_repositories_github` - 搜索GitHub仓库
- `create_repository_github` - 在账户中创建新的GitHub仓库
- `get_file_contents_github` - 获取GitHub仓库中文件或目录的内容
- `create_or_update_file_github` - 在GitHub仓库中创建或更新单个文件
- `push_files_github` - 单次提交推送多个文件到GitHub仓库
- `create_issue_github` - 在GitHub仓库中创建新issue
- `list_issues_github` - 列出GitHub仓库中的issues，支持过滤选项
- `get_issue_github` - 获取GitHub仓库中特定issue的详情
- `update_issue_github` - 更新GitHub仓库中的现有issue
- `add_issue_comment_github` - 为现有issue添加评论
- `create_pull_request_github` - 在GitHub仓库中创建新pull request
- `list_pull_requests_github` - 列出并过滤仓库pull requests
- `get_pull_request_github` - 获取特定pull request的详情
- `merge_pull_request_github` - 合并pull request
- `create_pull_request_review_github` - 为pull request创建审查
- `fork_repository_github` - Fork GitHub仓库到账户或指定组织
- `create_branch_github` - 在GitHub仓库中创建新分支
- `list_commits_github` - 获取GitHub仓库分支的提交列表
- `search_code_github` - 在GitHub仓库中搜索代码
- `search_issues_github` - 在GitHub仓库中搜索issues和pull requests
- `search_users_github` - 在GitHub上搜索用户
- `get_pull_request_files_github` - 获取pull request中更改的文件列表
- `get_pull_request_status_github` - 获取pull request所有状态检查的综合状态
- `get_pull_request_comments_github` - 获取pull request的审查评论
- `get_pull_request_reviews_github` - 获取pull request的审查
- `update_pull_request_branch_github` - 用基础分支的最新更改更新pull request分支

### 📚 文档查询
- `resolve-library-id_Context_7` - 解析包/产品名称为Context7兼容的库ID
- `get-library-docs_Context_7` - 获取库的最新文档，需要Context7兼容的库ID
- `deepwiki_fetch_mcp-deepwiki` - 获取deepwiki.com仓库并返回Markdown格式
- `convert_to_markdown_markitdown-mcp` - 将各种格式资源转换为Markdown

### 📄 API文档
- `read_project_oas_kfqpki_TikHub_io_API_Docs` - 读取TikHub.io API文档内容
- `read_project_oas_ref_resources_kfqpki_TikHub_io_API_Docs` - 读取TikHub API引用资源
- `refresh_project_oas_kfqpki_TikHub_io_API_Docs` - 刷新TikHub API文档
- `read_project_oas_ktkma4____API_-_API___` - 读取飞书API文档内容
- `read_project_oas_ref_resources_ktkma4____API_-_API___` - 读取飞书API引用资源
- `refresh_project_oas_ktkma4____API_-_API___` - 刷新飞书API文档

### 🖥️ 系统文件操作 (Desktop Commander)
- `get_config_Desktop_Commander` - 获取完整的服务器配置为JSON格式
- `set_config_value_Desktop_Commander` - 通过键设置特定配置值
- `read_file_Desktop_Commander` - 从文件系统或URL读取文件内容，支持偏移和长度参数
- `read_multiple_files_Desktop_Commander` - 同时读取多个文件的内容
- `write_file_Desktop_Commander` - 写入或追加文件内容，标准做法是分块写入
- `create_directory_Desktop_Commander` - 创建新目录或确保目录存在
- `list_directory_Desktop_Commander` - 获取指定路径中所有文件和目录的详细列表
- `move_file_Desktop_Commander` - 移动或重命名文件和目录
- `search_files_Desktop_Commander` - 通过名称使用大小写不敏感的子字符串匹配查找文件
- `search_code_Desktop_Commander` - 使用ripgrep在文件内容中搜索文本/代码模式
- `get_file_info_Desktop_Commander` - 获取文件或目录的详细元数据
- `edit_block_Desktop_Commander` - 对文件应用精确的文本替换

### 🔧 系统进程管理 (Desktop Commander)
- `start_process_Desktop_Commander` - 启动新的终端进程，支持智能状态检测
- `read_process_output_Desktop_Commander` - 从运行进程读取输出，支持智能完成检测
- `interact_with_process_Desktop_Commander` - 向运行进程发送输入并自动接收响应
- `force_terminate_Desktop_Commander` - 强制终止运行的终端会话
- `list_sessions_Desktop_Commander` - 列出所有活动的终端会话
- `list_processes_Desktop_Commander` - 列出所有运行的进程
- `kill_process_Desktop_Commander` - 通过PID终止进程
- `get_usage_stats_Desktop_Commander` - 获取调试和分析的使用统计
- `give_feedback_to_desktop_commander_Desktop_Commander` - 在浏览器中打开反馈表单

### 📄 代码库和文件操作 (内置核心)
- `codebase-retrieval` - Augment的上下文引擎，搜索和检索代码库中的相关代码片段
- `git-commit-retrieval` - 基于Git提交历史搜索相关信息，了解代码变更历史
- `view` - 查看文件和目录内容，支持正则表达式搜索和范围查看
- `str-replace-editor` - 编辑现有文件，支持字符串替换和插入操作
- `save-file` - 创建新文件，限制内容最多300行
- `remove-files` - 删除文件，用户工作区中删除文件的唯一安全工具

### 🔧 进程和终端操作 (内置核心)
- `launch-process` - 启动shell命令和进程，支持等待和后台模式
- `read-process` - 读取进程输出，支持智能完成检测
- `write-process` - 向进程写入输入，用于交互式操作
- `kill-process` - 通过终端ID终止进程
- `list-processes` - 列出所有活动进程及其状态
- `read-terminal` - 读取VSCode终端输出，支持选中文本读取

### � GitHub集成 (内置核心)
- `github-api` - 调用GitHub API，支持仓库、问题、PR、提交等操作

### �📋 任务管理 (内置核心)
- `view_tasklist` - 查看当前任务列表
- `add_tasks` - 添加新任务或子任务
- `update_tasks` - 更新任务状态、名称和描述
- `reorganize_tasklist` - 重组任务列表结构

### 🔍 其他核心功能 (内置)
- `diagnostics` - 获取IDE问题诊断（错误、警告等）
- `remember` - 创建长期记忆，保存重要信息
- `open-browser` - 在默认浏览器中打开URL
- `view-range-untruncated` - 查看截断内容的特定范围
- `search-untruncated` - 在截断内容中搜索术语
- `render-mermaid` - 渲染Mermaid图表，支持交互式图表展示

---

## � 工具统计总览

**按功能分类统计：**
- GitHub集成: 27个 (外部工具)
- 浏览器操作: 24个 (Playwright)
- 高级任务管理: 15个 (Shrimp Task Manager - 已停用)
- 系统文件操作: 12个 (Desktop Commander)
- 网络搜索: 9个 (各类搜索工具)
- 系统进程管理: 9个 (Desktop Commander)
- PromptX专业增强: 8个
- 网页抓取: 8个 (Firecrawl)
- 代码库和文件操作: 6个 (内置核心)
- 进程和终端操作: 6个 (内置核心)
- API文档: 6个
- 其他核心功能: 6个 (内置)
- 文档查询: 4个
- 任务管理: 4个 (内置核心)
- GitHub集成: 1个 (内置核心)
- 思维分析: 1个

**总计: 128个实际可用工具** (已移除16个不可用的Shrimp Task Manager工具)

## 🗂️ 已停用工具

> 以下工具在当前环境中不可用，但保留记录以供参考

### 📄 API文档 (已停用)
- `read_project_oas_ht1rcs_TikHub_io_API_Docs` - 旧版TikHub.io API文档工具 (已更新为kfqpki版本)
- `read_project_oas_ref_resources_ht1rcs_TikHub_io_API_Docs` - 旧版TikHub API引用资源工具
- `refresh_project_oas_ht1rcs_TikHub_io_API_Docs` - 旧版TikHub API文档刷新工具
- `read_project_oas_ucsen6____API_-_API___` - 旧版飞书API文档工具 (已更新为ktkma4版本)
- `read_project_oas_ref_resources_ucsen6____API_-_API___` - 旧版飞书API引用资源工具
- `refresh_project_oas_ucsen6____API_-_API___` - 旧版飞书API文档刷新工具

### 📚 文档查询 (已停用)
- `resolve-library-id_context7` - 旧版Context7库ID解析工具 (已更新为Context_7版本)
- `get-library-docs_context7` - 旧版Context7文档获取工具 (已更新为Context_7版本)

### 🦐 高级任务管理 (Shrimp Task Manager) - 完全不可用
- `plan_task_shrimp-task-manager` - 接收任务规划指导，构建复杂特性的步骤指导 **[工具不存在]**
- `analyze_task_shrimp-task-manager` - 深度分析任务需求，系统性检查代码库和技术可行性 **[工具不存在]**
- `reflect_task_shrimp-task-manager` - 批判性审查分析结果，评估解决方案完整性 **[工具不存在]**
- `split_tasks_shrimp-task-manager` - 将复杂任务分解为独立子任务，建立依赖关系和优先级 **[工具不存在]**
- `list_tasks_shrimp-task-manager` - 生成结构化任务列表，包含状态跟踪和依赖关系 **[工具不存在]**
- `execute_task_shrimp-task-manager` - 获取特定任务的执行指导，提供步骤化完成指南 **[工具不存在]**
- `verify_task_shrimp-task-manager` - 验证任务完成质量，根据验证标准进行评分 **[工具不存在]**
- `delete_task_shrimp-task-manager` - 删除未完成任务，保持系统记录完整性 **[工具不存在]**
- `clear_all_tasks_shrimp-task-manager` - 清除未完成任务并重置任务列表 **[工具不存在]**
- `update_task_shrimp-task-manager` - 更新任务内容，包括名称、描述、依赖关系等 **[工具不存在]**
- `query_task_shrimp-task-manager` - 基于关键词或ID搜索任务，显示简化任务信息 **[工具不存在]**
- `get_task_detail_shrimp-task-manager` - 获取任务的完整详细信息，包括实施指南 **[工具不存在]**
- `process_thought_shrimp-task-manager` - 进行灵活可演进的思维过程，逐步深化理解 **[工具不存在]**
- `research_mode_shrimp-task-manager` - 进入专业化研究模式，系统性研究技术主题 **[工具不存在]**
- `init_project_rules_shrimp-task-manager` - 初始化项目标准，生成或更新项目标准文档 **[工具不存在]**

> **问题说明**: 整个Shrimp Task Manager工具集在当前环境中不可用，可能未正确安装或配置。建议使用内置任务管理工具作为替代方案。

## 📝 更新日志

### v2.4.0 (2025-01-29 20:30)
**主要变更**:
- ✅ **内置核心工具重新分类**: 将内置工具按功能重新组织为5个核心类别
- 📝 **完善工具描述**: 更新所有内置工具的功能描述，提供更准确的使用指导
- 🔧 **新增工具**: 添加`git-commit-retrieval`工具到代码库操作类别
- 📊 **优化分类结构**: 将内置工具分为代码库操作、进程管理、GitHub集成、任务管理和其他核心功能

**具体更新**:
1. **内置工具重新分类**:
   - 代码库和文件操作 (6个): 包含codebase-retrieval、git-commit-retrieval、view等
   - 进程和终端操作 (6个): 包含launch-process、read-process等
   - GitHub集成 (1个): github-api工具
   - 任务管理 (4个): 完整的任务管理工具集
   - 其他核心功能 (6个): diagnostics、remember、render-mermaid等

2. **功能描述优化**:
   - 所有工具描述更加准确和详细
   - 突出每个工具的核心功能和使用场景
   - 统一描述格式和风格

3. **统计数据更新**:
   - 重新统计各类别工具数量
   - 明确区分内置核心工具和外部工具
   - 更新工具总数统计

### v2.3.0 (2025-01-29 18:00)
**主要变更**:
- ✅ **全面工具验证**: 完成65个工具的实际可用性验证
- ❌ **移除不可用工具**: 将16个Shrimp Task Manager工具移至已停用章节
- 📊 **更新统计数据**: 工具总数从144个调整为128个实际可用工具
- 🔍 **验证覆盖率**: 45.1%的工具已完成验证，可用率93.8%

**具体更新**:
1. **验证结果**:
   - 已验证65个工具，其中61个可用，4个不可用
   - PromptX工具集(8个)、API文档工具(6个)、时间工具(2个)等核心功能100%可用
   - 浏览器操作、GitHub集成、文档查询等大部分工具验证可用

2. **问题发现**:
   - Shrimp Task Manager整个工具集(16个)完全不可用
   - `web-fetch`工具存在网络访问限制
   - 部分工具类别仍需进一步验证

3. **索引优化**:
   - 重新组织已停用工具章节
   - 更新工具分类统计
   - 完善工具状态标记

### v2.2.0 (2025-01-29 16:45)
**主要变更**:
- ✅ **工具名称更新**: 修正了Context7和API文档工具的实际名称
- ✅ **新增已停用工具章节**: 记录不再可用的工具版本
- ✅ **验证工具可用性**: 通过实际调用验证了关键工具的状态

**具体更新**:
1. **Context7工具名称修正**:
   - `resolve-library-id_context7` → `resolve-library-id_Context_7`
   - `get-library-docs_context7` → `get-library-docs_Context_7`

2. **API文档工具名称修正**:
   - TikHub.io: `ht1rcs` → `kfqpki`
   - 飞书API: `ucsen6` → `ktkma4`

3. **验证结果**:
   - ✅ PromptX工具集 (8个) - 全部可用
   - ✅ Context7工具 (2个) - 名称已更新
   - ✅ API文档工具 (6个) - 名称已更新

**下次更新计划**:
- 重新统计工具总数
- 验证更多工具类别的可用性
- 完善工具分类和描述

---

*AI工具调用索引 - 快速查找版*
